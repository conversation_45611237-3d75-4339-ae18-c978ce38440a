# Sale Details Integrity Validation Command

## Overview

The `distribution:validate-sale-details` command validates the integrity of Sale distribution by checking that each Sale's quantity matches the sum of its associated SaleDetail quantities. This ensures mathematical consistency between parent sales and their detail breakdowns.

## Command Signature

```bash
php artisan distribution:validate-sale-details
```

## Required Parameters

- `--from-date=YYYY-MM-DD` - Start date for validation
- `--to-date=YYYY-MM-DD` - End date for validation

## Optional Parameters

- `--distributor-ids=1,2,3` - Comma-separated distributor IDs to filter
- `--product-ids=1,2,3` - Comma-separated product IDs to filter  
- `--tolerance=0.001` - Tolerance for rounding errors (default: 0.001)
- `--detailed` - Show detailed breakdown of problematic sales
- `--export-csv=filename` - Export results to CSV file

## Usage Examples

### Basic Validation
```bash
./vendor/bin/sail artisan distribution:validate-sale-details \
  --from-date=2024-01-01 \
  --to-date=2024-01-31
```

### Detailed Validation with Custom Tolerance
```bash
./vendor/bin/sail artisan distribution:validate-sale-details \
  --from-date=2024-01-01 \
  --to-date=2024-01-31 \
  --detailed \
  --tolerance=0.01
```

### Filtered Validation
```bash
./vendor/bin/sail artisan distribution:validate-sale-details \
  --from-date=2024-01-01 \
  --to-date=2024-01-31 \
  --distributor-ids=1,2,3 \
  --product-ids=4,5,6
```

### Validation with CSV Export
```bash
./vendor/bin/sail artisan distribution:validate-sale-details \
  --from-date=2024-01-01 \
  --to-date=2024-01-31 \
  --export-csv=sale_details_validation_jan2024
```

## Output

The command provides:

1. **Summary Statistics**
   - Total sales checked
   - Healthy sales count
   - Problematic sales count
   - Sales with no details count

2. **Problematic Sales Table**
   - Sale ID and quantity
   - Sum of related SaleDetail quantities
   - Difference/mismatch amount
   - Distributor and Product IDs
   - Date

3. **Detailed Breakdown** (when `--detailed` flag is used)
   - Breakdown by distributor
   - Breakdown by product
   - Breakdown by date

4. **Final Status**
   - ✅ VALIDATION PASSED: All sales have matching quantities
   - ❌ VALIDATION FAILED: Issues found with specific counts

## Validation Logic

The command checks each Sale record against its related SaleDetail records:

1. **Healthy Sales**: `|Sale.quantity - SUM(SaleDetail.quantity)| <= tolerance`
2. **Problematic Sales**: Difference exceeds tolerance threshold
3. **Sales with No Details**: Sales that have quantity > 0 but no SaleDetail records

## Edge Cases Handled

- Sales with no detail records
- Orphaned detail records (handled by LEFT JOIN)
- Rounding errors (configurable tolerance)
- Empty date ranges
- Invalid filter parameters

## Laravel Octane Compatibility

The command follows Laravel Octane best practices:
- Uses dependency injection
- No static state
- Proper memory management
- Uses Eloquent ORM with connection pooling

## Error Handling

- Validates date format (YYYY-MM-DD)
- Ensures from-date is not after to-date
- Handles database connection errors gracefully
- Provides detailed error messages and stack traces

## CSV Export

When using `--export-csv`, results are saved to `storage/app/exports/` with columns:
- Sale ID
- Status (HEALTHY/PROBLEMATIC/NO_DETAILS)
- Sale Quantity
- Details Sum
- Difference
- Details Count
- Distributor ID
- Product ID
- Date

## Performance Considerations

- Uses efficient SQL with GROUP BY and aggregation
- Processes large datasets in single query
- Memory-efficient result processing
- Suitable for production environments

## Integration with Existing Commands

This command complements other distribution validation commands:
- `distribution:validate-integrity` - Overall distribution validation
- `distribution:scan-errors` - Error scanning
- `distribution:generate-audit-report` - Audit reporting

## Getting Help

```bash
./vendor/bin/sail artisan distribution:validate-sale-details --help
```
